'use client';

import { storageService } from './storage-service';
import { supabase } from 'src/lib/supabase';

/**
 * Optimized Message Image Service
 * Tối ưu hóa xử lý upload hình ảnh cho message formatting
 */

// =====================================================
// CONSTANTS
// =====================================================

export const MESSAGE_IMAGE_CONFIG = {
  BUCKET: 'public',
  FOLDER: 'message-images',
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  MAX_IMAGES: 3
};

// =====================================================
// 2. VALIDATION FUNCTIONS
// =====================================================

export function validateImageFile(file) {
  const errors = [];

  if (!MESSAGE_IMAGE_CONFIG.ALLOWED_TYPES.includes(file.type)) {
    errors.push('Định dạng file không được hỗ trợ');
  }

  if (file.size > MESSAGE_IMAGE_CONFIG.MAX_FILE_SIZE) {
    const maxSizeMB = MESSAGE_IMAGE_CONFIG.MAX_FILE_SIZE / (1024 * 1024);
    errors.push(`Kích thước vượt quá ${maxSizeMB}MB`);
  }

  return { isValid: errors.length === 0, errors };
}

export function validateImageFiles(files) {
  const errors = [];

  if (files.length > MESSAGE_IMAGE_CONFIG.MAX_IMAGES) {
    errors.push(`Tối đa ${MESSAGE_IMAGE_CONFIG.MAX_IMAGES} hình ảnh`);
  }

  files.forEach((file, index) => {
    const fileValidation = validateImageFile(file);
    if (!fileValidation.isValid) {
      errors.push(`File ${index + 1}: ${fileValidation.errors.join(', ')}`);
    }
  });

  return { isValid: errors.length === 0, errors };
}

// =====================================================
// 3. UPLOAD FUNCTIONS
// =====================================================

/**
 * Get tenant ID for current user
 * @returns {Promise<string|null>} - Tenant ID
 */
async function getCurrentTenantId() {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return null;
    }

    // Lấy tenant_id từ bảng users
    const { data } = await supabase
      .from('users')
      .select('tenant_id')
      .eq('id', user.id)
      .single();

    return data?.tenant_id || null;
  } catch (error) {
    console.error('Error getting tenant ID:', error);
    return null;
  }
}

/**
 * Upload single image for message formatting (temporary upload)
 * Chỉ upload lên storage, chưa lưu vào database
 * @param {File} imageFile - Image file to upload
 * @returns {Promise<Object>} - Upload result with temporary flag
 */
export async function uploadMessageImageTemporary(imageFile) {
  try {
    // Validate file
    const validation = validateImageFile(imageFile);
    if (!validation.isValid) {
      return {
        success: false,
        error: { message: validation.errors.join(', ') },
        data: null
      };
    }

    // Get tenant ID
    const tenantId = await getCurrentTenantId();
    if (!tenantId) {
      return {
        success: false,
        error: { message: 'Không thể xác định tenant ID' },
        data: null
      };
    }

    // Generate unique filename
    const fileName = storageService.generateUniqueFileName(imageFile.name);

    // Build file path with tenant isolation - add temp prefix
    const filePath = storageService.buildFilePath(`${MESSAGE_IMAGE_CONFIG.FOLDER}/temp`, tenantId, fileName);

    console.log('Uploading temporary message image to path:', filePath);

    // Upload using existing storageService
    const uploadResult = await storageService.uploadFile(
      MESSAGE_IMAGE_CONFIG.BUCKET,
      filePath,
      imageFile,
      {
        upsert: true,
        cacheControl: '3600',
        contentType: imageFile.type
      }
    );

    if (!uploadResult.success) {
      console.error('Failed to upload temporary message image:', uploadResult.error);
      return {
        success: false,
        error: uploadResult.error,
        data: null
      };
    }

    console.log('Temporary message image uploaded successfully:', uploadResult.publicUrl);

    return {
      success: true,
      data: {
        id: `img_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        url: uploadResult.publicUrl,
        alt: imageFile.name,
        fileName,
        filePath,
        isTemporary: true, // Flag để đánh dấu là temporary
        uploadedAt: new Date().toISOString()
      },
      error: null
    };

  } catch (error) {
    console.error('Error uploading temporary message image:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi upload hình ảnh' },
      data: null
    };
  }
}

/**
 * Move temporary image to permanent location
 * @param {Object} tempImageData - Temporary image data
 * @returns {Promise<Object>} - Permanent image data
 */
export async function moveImageToPermanent(tempImageData) {
  try {
    if (!tempImageData.isTemporary) {
      // Already permanent
      return {
        success: true,
        data: { ...tempImageData, isTemporary: false },
        error: null
      };
    }

    const tenantId = await getCurrentTenantId();
    if (!tenantId) {
      return {
        success: false,
        error: { message: 'Không thể xác định tenant ID' },
        data: null
      };
    }

    // Create permanent file path
    const permanentFilePath = storageService.buildFilePath(MESSAGE_IMAGE_CONFIG.FOLDER, tenantId, tempImageData.fileName);

    // Copy from temp to permanent location
    const copyResult = await storageService.copyFile(
      MESSAGE_IMAGE_CONFIG.BUCKET,
      tempImageData.filePath,
      permanentFilePath
    );

    if (!copyResult.success) {
      console.error('Failed to copy image to permanent location:', copyResult.error);
      return {
        success: false,
        error: copyResult.error,
        data: null
      };
    }

    // Delete temporary file
    await storageService.deleteFiles(MESSAGE_IMAGE_CONFIG.BUCKET, tempImageData.filePath);

    // Get new public URL
    const publicUrlResult = storageService.getPublicUrl(MESSAGE_IMAGE_CONFIG.BUCKET, permanentFilePath);

    return {
      success: true,
      data: {
        ...tempImageData,
        url: publicUrlResult.publicUrl,
        filePath: permanentFilePath,
        isTemporary: false
      },
      error: null
    };

  } catch (error) {
    console.error('Error moving image to permanent location:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi di chuyển hình ảnh' },
      data: null
    };
  }
}

/**
 * Upload multiple images for message formatting (temporary)
 * @param {Array<File>} imageFiles - Array of image files to upload
 * @returns {Promise<Object>} - Upload result
 */
export async function uploadMessageImagesTemporary(imageFiles) {
  try {
    // Validate files
    const validation = validateImageFiles(imageFiles);
    if (!validation.isValid) {
      return {
        success: false,
        error: { message: validation.errors.join(', ') },
        data: null
      };
    }

    // Upload each file as temporary
    const uploadPromises = imageFiles.map(file => uploadMessageImageTemporary(file));
    const results = await Promise.all(uploadPromises);

    // Check for failures
    const failedUploads = results.filter(result => !result.success);
    const successfulUploads = results.filter(result => result.success);

    if (failedUploads.length > 0) {
      // Some uploads failed - cleanup successful ones
      const cleanupPromises = successfulUploads.map(result =>
        deleteMessageImage(result.data.filePath)
      );
      await Promise.all(cleanupPromises);

      const errorMessages = failedUploads.map(result => result.error?.message || 'Unknown error');
      return {
        success: false,
        error: { message: `Một số file upload thất bại: ${errorMessages.join(', ')}` },
        data: null
      };
    }

    // All uploads successful
    return {
      success: true,
      data: successfulUploads.map(result => result.data),
      error: null
    };

  } catch (error) {
    console.error('Error uploading temporary message images:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi upload hình ảnh' },
      data: null
    };
  }
}

/**
 * Process images for saving - move temporary to permanent and return URLs only
 * Similar to product-service pattern: only store publicUrl in database
 * @param {Array} images - Array of image objects (mix of temporary and permanent)
 * @returns {Promise<Object>} - Processed images result with URLs only
 */
export async function processImagesForSaving(images) {
  try {
    if (!images?.length) {
      return { success: true, data: [], error: null };
    }

    const processPromises = images.map(async (image) => {
      // Handle string URLs (already permanent)
      if (typeof image === 'string') {
        return { success: true, data: image, error: null };
      }

      // Handle image objects
      if (image.isTemporary) {
        const moveResult = await moveImageToPermanent(image);
        if (moveResult.success) {
          return { success: true, data: moveResult.data.url, error: null };
        }
        return moveResult;
      } else {
        // Already permanent, return URL only
        return { success: true, data: image.url || image, error: null };
      }
    });

    const results = await Promise.all(processPromises);

    // Check for failures
    const failedProcesses = results.filter(result => !result.success);
    const successfulProcesses = results.filter(result => result.success);

    if (failedProcesses.length > 0) {
      const errorMessages = failedProcesses.map(result => result.error?.message || 'Unknown error');
      return {
        success: false,
        error: { message: `Một số hình ảnh xử lý thất bại: ${errorMessages.join(', ')}` },
        data: null
      };
    }

    const imageUrls = successfulProcesses.map(result => result.data);

    return { success: true, data: imageUrls, error: null };

  } catch (error) {
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi xử lý hình ảnh' },
      data: null
    };
  }
}

// =====================================================
// 4. DELETE FUNCTIONS
// =====================================================

/**
 * Delete message image from storage
 * @param {string} filePath - File path in storage
 * @returns {Promise<Object>} - Delete result
 */
export async function deleteMessageImage(filePath) {
  try {
    const deleteResult = await storageService.deleteFiles(MESSAGE_IMAGE_CONFIG.BUCKET, filePath);

    if (!deleteResult.success) {
      console.error('Failed to delete message image:', deleteResult.error);
      return {
        success: false,
        error: deleteResult.error
      };
    }

    console.log('Message image deleted successfully:', filePath);
    return {
      success: true,
      error: null
    };

  } catch (error) {
    console.error('Error deleting message image:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi xóa hình ảnh' }
    };
  }
}

/**
 * Delete multiple message images from storage
 * @param {Array<string>} filePaths - Array of file paths in storage
 * @returns {Promise<Object>} - Delete result
 */
export async function deleteMessageImages(filePaths) {
  try {
    if (!filePaths || filePaths.length === 0) {
      return { success: true, error: null };
    }

    // Use batch delete for better performance
    const deleteResult = await storageService.deleteFiles(MESSAGE_IMAGE_CONFIG.BUCKET, filePaths);

    if (!deleteResult.success) {
      console.error('Failed to delete message images:', deleteResult.error);
      return {
        success: false,
        error: deleteResult.error
      };
    }

    console.log('All message images deleted successfully:', filePaths.length);
    return {
      success: true,
      error: null
    };

  } catch (error) {
    console.error('Error deleting message images:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi xóa hình ảnh' }
    };
  }
}

// =====================================================
// 5. UTILITY FUNCTIONS
// =====================================================

/**
 * Extract file path from public URL
 * @param {string} publicUrl - Public URL of the image
 * @returns {string|null} - File path or null if invalid
 */
export function extractFilePathFromUrl(publicUrl) {
  try {
    const url = new URL(publicUrl);
    const pathParts = url.pathname.split('/');

    // Remove empty parts and 'storage/v1/object/public/public'
    const cleanParts = pathParts.filter(part => part && part !== 'storage' && part !== 'v1' && part !== 'object' && part !== 'public');

    // Remove the first 'public' (bucket name)
    if (cleanParts[0] === 'public') {
      cleanParts.shift();
    }

    return cleanParts.join('/');
  } catch (error) {
    console.error('Error extracting file path from URL:', error);
    return null;
  }
}

/**
 * Cleanup temporary images that are not used
 * @param {Array} tempImages - Array of temporary image objects
 * @returns {Promise<Object>} - Cleanup result
 */
export async function cleanupTemporaryImages(tempImages) {
  try {
    if (!tempImages || tempImages.length === 0) {
      console.log('✅ No temporary images to cleanup');
      return { success: true, error: null };
    }

    const tempImagePaths = tempImages
      .filter(img => img && img.isTemporary && img.filePath)
      .map(img => img.filePath);

    if (tempImagePaths.length === 0) {
      console.log('✅ No valid temporary image paths to cleanup');
      return { success: true, error: null };
    }

    console.log('🗑️ Cleaning up temporary images:', tempImagePaths.length);

    const deleteResult = await deleteMessageImages(tempImagePaths);

    if (deleteResult.success) {
      console.log('✅ Temporary images cleaned up successfully');
    } else {
      console.error('⚠️ Failed to cleanup some temporary images:', deleteResult.error);
      // Don't throw error, just log it - cleanup failures shouldn't break main flow
    }

    return deleteResult;

  } catch (error) {
    console.error('❌ Error cleaning up temporary images:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi dọn dẹp hình ảnh tạm' }
    };
  }
}

/**
 * Get images that need to be cleaned up (removed from current list)
 * @param {Array} oldImages - Previous image list
 * @param {Array} newImages - New image list
 * @returns {Array} - Images to be cleaned up
 */
export function getImagesToCleanup(oldImages, newImages) {
  if (!oldImages || oldImages.length === 0) {
    return [];
  }

  if (!newImages || newImages.length === 0) {
    return oldImages.filter(img => !img.isTemporary); // Only cleanup permanent images
  }

  const newImageIds = new Set(newImages.map(img => img.id));

  return oldImages.filter(img =>
    !newImageIds.has(img.id) && !img.isTemporary // Only cleanup permanent images that are removed
  );
}

/**
 * Validate and prepare images for form submission
 * @param {Array} images - Array of image objects
 * @returns {Object} - Validation and preparation result
 */
export function validateAndPrepareImages(images) {
  try {
    if (!images || images.length === 0) {
      return {
        isValid: true,
        images: [],
        errors: []
      };
    }

    const errors = [];
    const validImages = [];

    images.forEach((image, index) => {
      if (!image || typeof image !== 'object') {
        errors.push(`Hình ảnh ${index + 1} không hợp lệ`);
        return;
      }

      if (!image.id || !image.url) {
        errors.push(`Hình ảnh ${index + 1} thiếu thông tin cần thiết`);
        return;
      }

      // Add to valid images
      validImages.push({
        id: image.id,
        url: image.url,
        alt: image.alt || '',
        fileName: image.fileName || '',
        filePath: image.filePath || '',
        isTemporary: image.isTemporary || false,
        uploadedAt: image.uploadedAt || new Date().toISOString()
      });
    });

    return {
      isValid: errors.length === 0,
      images: validImages,
      errors
    };

  } catch (error) {
    console.error('Error validating and preparing images:', error);
    return {
      isValid: false,
      images: [],
      errors: ['Lỗi không xác định khi xử lý hình ảnh']
    };
  }
}
